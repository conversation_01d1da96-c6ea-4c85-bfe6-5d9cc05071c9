# n8n - Workflow automation tool

n8n is an extendable workflow automation tool. With a [fair-code](https://faircode.io) distribution model, n8n
will always have visible source code, be available to self-host, and allow you to add your own custom
functions, logic and apps. n8n's node-based approach makes it highly versatile, enabling you to connect
anything to everything.

![n8n.io - Screenshot](https://raw.githubusercontent.com/n8n-io/self-hosted-ai-starter-kit/main/assets/n8n-demo.gif)

## Available integrations

n8n has 200+ different nodes to automate workflows. The list can be found on:
[https://n8n.io/integrations](https://n8n.io/integrations)

# Remarketing Control Center

It's a centralized system handles handles contact management, routing of sms deliveries and reporting for Remarketing. It is also a tool to manage and run workflows for various different purposes.

## Custom integration with Salesforce Marketing Cloud

Mainly written with python scripts to handle delivery of SMS messages for the Remarketing Control Center. Specific python script can be called in a node to execute integrated solution of delivery.
