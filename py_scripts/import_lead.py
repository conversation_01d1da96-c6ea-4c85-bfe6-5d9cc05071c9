import mysql.connector
import psycopg2
from config import MYSQL_USER, MYSQL_PASSWORD, MYSQL_HOST, MYSQL_PORT, MYSQL_LEAD_DB, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE

MYSQL_CONFIG = {
    'user': MYSQL_USER,
    'password': MYSQL_PASSWORD,
    'host': MYSQL_HOST,
    'port': MYSQL_PORT,
    'database': MYSQL_LEAD_DB
}

PGSQL_CONFIG = {
    'dbname': PGSQL_DATABASE,
    'user': PGSQL_USER,
    'password': PGSQL_PASSWORD,
    'host': PGSQL_HOST
}

def fetch_data_from_mysql():
    try:
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor_mysql = mysql_conn.cursor(dictionary=True)

        query_mysql = """
        SELECT email AS emailaddress, id_brand AS brand_id, first_name AS firstname, 
               last_name AS lastname, NULL AS initial_cancel_date, NULL AS initial_expiration_date, 
               NULL AS initial_desc, NULL AS source, id_ad AS adid, id_partner AS pid, 
               NULL AS reptype, NULL AS affordability, 
               lead_date AS leaddate, customer_ip AS ip_address, NULL AS eo_type, 
               id_affiliate AS affiliate_id, NULL AS eo_promotion_id, address1 AS customer_address_1, 
               address2 AS customer_address_2, city AS customer_city, state AS customer_state, 
               REPLACE(JSON_EXTRACT(fields,'$.original_adid'),'"','') AS original_adid, NULL AS initial_signup_date, 
               NULL AS initial_decline_date, zip AS geo_zip, capture_location AS eo_capture_location, 
               NULL AS registration_step, vertical_type AS vertical_type, zip AS customer_zip, NULL AS bucket, 
               zip AS zip, NULL AS initial_cancel_number, CONCAT('1', phone) AS customer_phone, NULL AS enrollment_status, 
               NULL AS verification_status, NULL AS self_credit_rating, NULL AS credit_check_frequency, NULL AS split_id, 
               'US' AS locale, REPLACE(JSON_EXTRACT(fields,'$.tcpaOptin'),'"','') AS tcpa_optin, 
               last_modified AS lead_modified_date, NULL AS creditcardseen, NULL AS creditcardinitiated, NULL AS journey, 
               REPLACE(JSON_EXTRACT(fields, '$.carrier'),'"','') AS carrier, 
               REPLACE(JSON_EXTRACT(fields, '$.carrierParent'),'"','') AS carrierparent, 
               REPLACE(JSON_EXTRACT(fields, '$.blacklisted'),'"','') AS blacklisted, 
               REPLACE(JSON_EXTRACT(fields, '$.status'),'"','') AS status, 
               REPLACE(JSON_EXTRACT(fields, '$.type'),'"','') AS type, 
               REPLACE(JSON_EXTRACT(fields, '$.globalUuid'),'"','') AS global_uuid, 
               REPLACE(JSON_EXTRACT(fields, '$.shortURL'),'"','') AS shorturl, 
               REPLACE(JSON_EXTRACT(fields, '$.pl_shortURL'),'"','') AS pl_shorturl, jluvr AS jluvr, id_lead AS lead_id
        FROM nic_common.lead 
        WHERE lead_type = 'internal_sms' 
        ORDER BY id_lead DESC 
        LIMIT 10;
        """
        
        cursor_mysql.execute(query_mysql)
        rows = cursor_mysql.fetchall()

    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return []
    
    finally:
        if cursor_mysql:
            cursor_mysql.close()
        if mysql_conn:
            mysql_conn.close()

    return rows

def insert_data_into_postgres(rows):
    try:
        postgres_conn = psycopg2.connect(**PGSQL_CONFIG)
        cursor_postgres = postgres_conn.cursor()

        for row in rows:
            columns = ', '.join(row.keys())
            values = tuple(row.values())

            placeholders = ', '.join(['%s'] * len(values))
            query_insert = f"INSERT INTO subscriber.subscriber_sms ({columns}) VALUES ({placeholders})"
            
            cursor_postgres.execute(query_insert, values)

        postgres_conn.commit()

    except psycopg2.Error as err:
        print(f"Error: {err}")
    
    finally:
        if cursor_postgres:
            cursor_postgres.close()
        if postgres_conn:
            postgres_conn.close()

def main():
    rows = fetch_data_from_mysql()
    if rows:
        insert_data_into_postgres(rows)

if __name__ == "__main__":
    main()