import psycopg2
from psycopg2.extras import RealDictCursor
import pandas as pd
from simple_salesforce import Salesforce
import requests
from datetime import datetime, timedelta
import pytz
from config import MCG_USERNAME, MCG_PASSWORD, MCG_SECURITY_TOKEN, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE

username = MCG_USERNAME
password = MCG_PASSWORD
security_token = MCG_SECURITY_TOKEN

sf = Salesforce(username=username, password=password, security_token=security_token)

conn_pgsql = psycopg2.connect(
    dbname=PGSQL_DATABASE,
    user=PGSQL_USER,
    password=PGSQL_PASSWORD,
    host=PGSQL_HOST
)
cursor = conn_pgsql.cursor(cursor_factory=RealDictCursor)

query = "SELECT id_sms, phone, email, firstname, lastname, campaign_date, message FROM subscriber.transaction_sms WHERE platform = 'mcg' AND delivery_status IS NULL AND campaign_date = CURRENT_DATE"
cursor.execute(query)
rows = cursor.fetchall()

def upload_contact(row):
    query = f"SELECT Id, message__c FROM Contact WHERE Email = '{row['email']}' OR Phone = '{row['phone']}' LIMIT 1"
    contacts = sf.query_all(query)

    if contacts['totalSize'] > 0:
        contact_id = contacts['records'][0]['Id']
        campaign_date_str = (row['campaign_date'] + timedelta(hours=9)).strftime('%Y-%m-%d')
        sf.Contact.update(contact_id, {'campaign_date__c': campaign_date_str, 'message__c': row['message']})
    else:
        contact = {
            'FirstName': row['firstname'],
            'LastName': row['lastname'],
            'Email': row['email'],
            'Phone': row['phone'],
            'campaign_date__c': campaign_date_str,
            'message__c': row['message']
        }
        result = sf.Contact.create(contact)

for row in rows:
    try:
        result = upload_contact(row)
        delivery_date = datetime.now().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('US/Pacific'))
        update_query = """
        UPDATE subscriber.transaction_sms 
        SET delivery_status = %s, delivery_date = %s, response_mcg = %s 
        WHERE id_sms = %s
        """
        cursor.execute(update_query, ('success', delivery_date, str(result), row['id_sms']))
        conn_pgsql.commit()
    except Exception as e:
        update_query = """
        UPDATE subscriber.transaction_sms 
        SET delivery_status = %s, delivery_date = %s, response_mcg = %s 
        WHERE id_sms = %s
        """
        cursor.execute(update_query, ('error', delivery_date, str(e), row['id_sms']))
        conn_pgsql.commit()

cursor.close()
conn_pgsql.close()
