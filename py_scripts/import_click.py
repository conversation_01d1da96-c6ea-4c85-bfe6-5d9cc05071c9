import mysql.connector
import psycopg2
from config import MYSQL_USER, MYSQL_PASSWORD, MYSQL_HOST, MYSQL_PORT, MYSQL_CLICK_DB, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE

MYSQL_CONFIG = {
    'user': MYSQL_USER,
    'password': MYSQL_PASSWORD,
    'host': MYSQL_HOST,
    'port': MYSQL_PORT,
    'database': MYSQL_CLICK_DB
}

PGSQL_CONFIG = {
    'dbname': PGSQL_DATABASE,
    'user': PGSQL_USER,
    'password': PGSQL_PASSWORD,
    'host': PGSQL_HOST
}

def fetch_data_from_mysql():
    try:
        mysql_conn = mysql.connector.connect(**MYSQL_CONFIG)
        cursor_mysql = mysql_conn.cursor(dictionary=True)

        query_mysql = """
        SELECT short_url, xid, brand_id, email, CONCAT('1',phone) as phone, afid, last_accessed, jluvr, campaign_date
        FROM nic_shorturl.short_url 
        WHERE last_accessed = CURDATE()
        ORDER BY id_lead DESC 
        LIMIT 10;
        """
        
        cursor_mysql.execute(query_mysql)
        rows = cursor_mysql.fetchall()

    except mysql.connector.Error as err:
        print(f"Error: {err}")
        return []
    
    finally:
        if cursor_mysql:
            cursor_mysql.close()
        if mysql_conn:
            mysql_conn.close()

    return rows

def insert_data_into_postgres(rows):
    try:
        postgres_conn = psycopg2.connect(**PGSQL_CONFIG)
        cursor_postgres = postgres_conn.cursor()

        for row in rows:
            columns = ', '.join(row.keys())
            values = tuple(row.values())

            placeholders = ', '.join(['%s'] * len(values))
            query_insert = f"INSERT INTO subscriber.click_sms ({columns}) VALUES ({placeholders})"
            
            cursor_postgres.execute(query_insert, values)

        postgres_conn.commit()

    except psycopg2.Error as err:
        print(f"Error: {err}")
    
    finally:
        if cursor_postgres:
            cursor_postgres.close()
        if postgres_conn:
            postgres_conn.close()

def main():
    rows = fetch_data_from_mysql()
    if rows:
        insert_data_into_postgres(rows)

if __name__ == "__main__":
    main()