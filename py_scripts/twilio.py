import psycopg2
from psycopg2 import Error
from config import TWILIO_ACCOUNT_SID, TWILIO_AUTH_TOKEN, PGSQL_USER, PGSQL_PASSWORD, PGSQL_HOST, PGSQL_DATABASE
from twilio.rest import Client
from datetime import datetime
import pytz

account_sid = TWILIO_ACCOUNT_SID
auth_token = TWILIO_AUTH_TOKEN
did = '+***********'
client = Client(account_sid, auth_token)

def get_sms_data_from_db():
    try:
        connection = psycopg2.connect(user=PGSQL_USER,
                                      password=PGSQL_PASSWORD,
                                      host=PGSQL_HOST,
                                      port="5432",
                                      database=PGSQL_DATABASE)
        if connection is not None:
            cursor = connection.cursor()
            query = """
                SELECT id_sms, phone, message 
                FROM subscriber.transaction_sms 
                WHERE platform = 'twilio' AND campaign_date = CURRENT_DATE AND delivery_status IS NULL
            """
            cursor.execute(query)
            records = cursor.fetchall()

            sms_data = []
            for record in records:
                id_sms, phone, message = record
                sms_data.append({
                    'id_sms': id_sms,
                    'phone': phone,
                    'message': message
                })
            return sms_data

    except Error as e:
        print("Error reading data from PostgreSQL table:", e)
    finally:
        if connection is not None:
            cursor.close()
            connection.close()

def update_delivery_status(id_sms, status, delivery_date, response_twilio):
    try:
        connection = psycopg2.connect(user=PGSQL_USER,
                                      password=PGSQL_PASSWORD,
                                      host=PGSQL_HOST,
                                      port="5432",
                                      database=PGSQL_DATABASE)
        if connection is not None:
            cursor = connection.cursor()
            update_query = """
                UPDATE subscriber.transaction_sms 
                SET delivery_status = %s, delivery_date = %s, response_twilio = %s 
                WHERE id_sms = %s
            """
            cursor.execute(update_query, (status, delivery_date, response_twilio, id_sms))
            connection.commit()

    except Error as e:
        print("Error updating delivery status in PostgreSQL table:", e)
    finally:
        if connection is not None:
            cursor.close()
            connection.close()

def main():
    sms_data = get_sms_data_from_db()

    for sms in sms_data:
        id_sms = sms['id_sms']
        phone_number = sms['phone']
        message_text = sms['message']
        delivery_date = datetime.now().replace(tzinfo=pytz.utc).astimezone(pytz.timezone('US/Pacific'))

        try:
            message = client.messages.create(to=phone_number, from_=did, body=message_text)

            update_delivery_status(id_sms, 'success' if message else 'error', delivery_date if message else None, str(message.sid) if message else None)
        except Exception as e:
            update_delivery_status(id_sms, 'error', delivery_date, str(e))

if __name__ == "__main__":
    main()
