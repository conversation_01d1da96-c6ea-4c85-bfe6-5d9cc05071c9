CREATE SCHEMA IF NOT EXISTS subscriber;

CREATE TABLE subscriber.brand (
    brand_id INT NOT NULL PRIMARY KEY,
    brand_name VARCHAR(255),
    vertical VARCHAR(255),
    brand_logo VARCHAR(255),
    primary_header_color VARCHAR(255),
    primary_header_cta_color VARCHAR(255),
    module_header_color VARCHAR(255),
    module_cta_color VARCHAR(255),
    module_price_color VARCHAR(255),
    module_number_block_color VARCHAR(255),
    module4_header_color VARCHAR(255),
    module5_cta_color VARCHAR(255),
    cta_color VARCHAR(255),
    corp_address1 VARCHAR(255),
    corp_address2 VARCHAR(255),
    email_link VARCHAR(255),
    privacy_policy_link VARCHAR(255),
    unsub_link VARCHAR(255),
    privacy_policy VARCHAR(255),
    sms_link VARCHAR(255),
    sms_optout VARCHAR(255),
    pl_link VARCHAR(255)
);

CREATE TABLE subscriber.click_sms (
    short_url VARCHAR(255),
    xid VARCHAR(255),
    brand_id INT,
    email VARCHAR(255),
    phone VARCHAR(255),
    afid VARCHAR(255),
    last_accessed TIMESTAMP
);

CREATE TABLE subscriber.content_sms (
    version VARCHAR(255) NOT NULL PRIMARY KEY,
    template VARCHAR(255),
    content_type VARCHAR(255),
    vertical VARCHAR(255),
    message VARCHAR(255),
    journey VARCHAR(255)
);

CREATE TABLE subscriber.optout_sms (
    phone VARCHAR(255) PRIMARY KEY,
    optout_date VARCHAR(255)
);

CREATE TABLE subscriber.pdb (
    rank INT,
    zip VARCHAR(255),
    brand_id INT,
    vertical_type VARCHAR(255),
    propid VARCHAR(255),
    propaddress VARCHAR(255),
    propcity VARCHAR(255),
    prop_seller_phone VARCHAR(255),
    prop_seller VARCHAR(255),
    propzip VARCHAR(255),
    saletype VARCHAR(255),
    homepic VARCHAR(255),
    homepic2 VARCHAR(255),
    homepic3 VARCHAR(255),
    propurl VARCHAR(255),
    bed VARCHAR(255),
    bath VARCHAR(255),
    sqfeet VARCHAR(255),
    pricename VARCHAR(255),
    price VARCHAR(255),
    proptype VARCHAR(255),
    valuename VARCHAR(255),
    value VARCHAR(255)
);

CREATE TABLE subscriber.subscriber_sms (
    EmailAddress VARCHAR(255) NOT NULL,
    brand_id INT NOT NULL,
    firstname VARCHAR(255),
    lastname VARCHAR(255),
    initial_cancel_date TIMESTAMP,
    initial_expiration_date TIMESTAMP,
    initial_desc VARCHAR(255),
    source VARCHAR(255),
    adid VARCHAR(255),
    pid VARCHAR(255),
    reptype VARCHAR(255),
    affordability VARCHAR(255),
    leaddate TIMESTAMP,
    ip_address VARCHAR(255),
    eo_type VARCHAR(255),
    affiliate_id VARCHAR(255),
    eo_promotion_id VARCHAR(255),
    customer_address_1 VARCHAR(255),
    customer_address_2 VARCHAR(255),
    customer_city VARCHAR(255),
    customer_state VARCHAR(255),
    original_adid VARCHAR(255),
    initial_signup_date TIMESTAMP,
    initial_decline_date TIMESTAMP,
    geo_zip VARCHAR(255),
    eo_capture_location VARCHAR(255),
    registration_step VARCHAR(255),
    vertical_type VARCHAR(255),
    customer_zip VARCHAR(255),
    bucket VARCHAR(255),
    zip VARCHAR(255),
    initial_cancel_number VARCHAR(255),
    customer_phone VARCHAR(255),
    enrollment_status VARCHAR(255),
    verification_status VARCHAR(255),
    self_credit_rating VARCHAR(255),
    credit_check_frequency VARCHAR(255),
    split_id VARCHAR(255),
    locale VARCHAR(255),
    tcpa_optin VARCHAR(255),
    lead_modified_date TIMESTAMP,
    creditCardSeen VARCHAR(255),
    CreditCardInitiated VARCHAR(255),
    Journey VARCHAR(255),
    Carrier VARCHAR(255),
    CarrierParent VARCHAR(255),
    Blacklisted VARCHAR(255),
    Status VARCHAR(255),
    Type VARCHAR(255),
    global_uuid VARCHAR(255),
    shorturl VARCHAR(255),
    pl_shorturl VARCHAR(255)
);

CREATE TABLE subscriber.transaction_sms (
    id_sms SERIAL PRIMARY KEY,
    phone VARCHAR(255),
    email VARCHAR(255),
    firstname VARCHAR(255),
    lastname VARCHAR(255),
    version VARCHAR(255),
    message VARCHAR(255),
    platform VARCHAR(255),
    campaign_date DATE,
    delivery_status VARCHAR(255),
    delivery_date TIMESTAMP,
    response_mcg VARCHAR(255),
    response_mce VARCHAR(255),
    response_twilio VARCHAR(255)
);

CREATE TABLE subscriber.zip (
    zip VARCHAR(255) PRIMARY KEY,
    city VARCHAR(255),
    state VARCHAR(255)
);
