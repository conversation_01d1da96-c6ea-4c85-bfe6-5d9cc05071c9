INSERT INTO `nic_common`.`lead_partner`(`id_lead_partner`, `name`, `decription`, `check_type`, `partner_identifier`) VALUES (5082, 'RCC', 'Remarketing Control Center', 'ping', NULL);
INSERT INTO `nic_common`.`lead_partner_submitter`(`id_lead_partner_submitter`, `name`, `description`, `url`, `request_timeout`, `lead_content_type`, `lead_send_method`, `lead_send_format`, `authorization_params`, `authorization_type`, `request_field_mapping`, `full_submission_request_template`, `email_from`, `email_to`, `email_subject`, `success_rule`, `failure_rule`, `retry_rule`, `additional_headers`) 
VALUES 
(156, 'RCC SMS FHC', 'Remarketing Control Center SMS Leads FHC', 'https://control-center.nationsinfocorp.com/webhook/30c96781-8827-410a-82e3-2fd54270000c', 20000, NULL, 'POST', 'JSO<PERSON>', NULL, NULL, NULL, '\'{\' \n  + \'\"emailaddress\": \' + #jsonMapper.writeValueAsString(#lead.email == null ? #fields[\'email\'] : #lead.email)\n  + \', \"brand_id\": 80\'\n  + \', \"firstname\": \' + #jsonMapper.writeValueAsString(#lead.firstName == null ? #fields[\'custfullname\'] : #lead.firstName)\n  + \', \"lastname\": \' + #jsonMapper.writeValueAsString(#lead.lastName)\n  + \', \"adid\": \' + #jsonMapper.writeValueAsString(#lead.idAd)\n  + \', \"pid\": \' + #jsonMapper.writeValueAsString(\"\" + #lead.idPartner)  \n  + \', \"leaddate\": \' + #jsonMapper.writeValueAsString(#lead.getFormattedLeadDate(\"yyyy-MM-dd hh:mm:ss a\"))\n  + \', \"customer_address_1\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_1\'])\n  + \', \"customer_address_2\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_2\'])\n  + \', \"customer_city\": \' + #jsonMapper.writeValueAsString(#lead.city)\n  + \', \"customer_state\": \' + #jsonMapper.writeValueAsString(#lead.state)\n  + \', \"original_adid\": \' + #jsonMapper.writeValueAsString(#fields[\'original_adid\'])\n  + \', \"vertical_type\": \' + #jsonMapper.writeValueAsString(#lead.verticalType)\n  + \', \"zip\": \' + #jsonMapper.writeValueAsString(#lead.zip)\n  + \', \"customer_phone\": \' + #jsonMapper.writeValueAsString(#lead.phone)\n  + \', \"verification_status\": \' + #jsonMapper.writeValueAsString(#fields[\'verification_status\'])\n  + \', \"tcpa_optin\": \' + #jsonMapper.writeValueAsString(#fields[\'tcpaOptin\'] == \'true\' ? 1 : 0)\n  + \', \"carrier\": \' + #jsonMapper.writeValueAsString(#fields[\'carrier\'])\n  + \', \"carrierparent\": \' + #jsonMapper.writeValueAsString(#fields[\'parentCarrier\'])\n  + \', \"blacklisted\": \' + #jsonMapper.writeValueAsString(#fields[\'blacklisted\'])\n  + \', \"status\": \' + #jsonMapper.writeValueAsString(#fields[\'status\'])\n  + \', \"type\": \' + #jsonMapper.writeValueAsString(#fields[\'type\'])\n  + \', \"shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'shortURL\'])\n  + \', \"pl_shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'pl_shorturl\'])\n  + \', \"jluvr\": \' + #jsonMapper.writeValueAsString(#lead.jluvr)\n  + \', \"lead_id\" : \' + #jsonMapper.writeValueAsString(\"\" + #lead.id)\n+ \'}\'', NULL, NULL, NULL, '#response.contains(\'success\')', NULL, NULL, '{\"Authorization\":\"Basic bjhuOkV0cGNSTE1rR29CdA==\"}'),
(157, 'RCC SMS FRTO', 'Remarketing Control Center SMS Leads FRTO', 'https://control-center.nationsinfocorp.com/webhook/2d781614-c618-4545-995a-1176e432ec94', 20000, NULL, 'POST', 'JSON', NULL, NULL, NULL, '\'{\' \n  + \'\"emailaddress\": \' + #jsonMapper.writeValueAsString(#lead.email == null ? #fields[\'email\'] : #lead.email)\n  + \', \"brand_id\": 37\'\n  + \', \"firstname\": \' + #jsonMapper.writeValueAsString(#lead.firstName == null ? #fields[\'custfullname\'] : #lead.firstName)\n  + \', \"lastname\": \' + #jsonMapper.writeValueAsString(#lead.lastName)\n  + \', \"adid\": \' + #jsonMapper.writeValueAsString(#lead.idAd)\n  + \', \"pid\": \' + #jsonMapper.writeValueAsString(\"\" + #lead.idPartner)  \n  + \', \"leaddate\": \' + #jsonMapper.writeValueAsString(#lead.getFormattedLeadDate(\"yyyy-MM-dd hh:mm:ss a\"))\n  + \', \"customer_address_1\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_1\'])\n  + \', \"customer_address_2\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_2\'])\n  + \', \"customer_city\": \' + #jsonMapper.writeValueAsString(#lead.city)\n  + \', \"customer_state\": \' + #jsonMapper.writeValueAsString(#lead.state)\n  + \', \"original_adid\": \' + #jsonMapper.writeValueAsString(#fields[\'original_adid\'])\n  + \', \"vertical_type\": \' + #jsonMapper.writeValueAsString(#lead.verticalType)\n  + \', \"zip\": \' + #jsonMapper.writeValueAsString(#lead.zip)\n  + \', \"customer_phone\": \' + #jsonMapper.writeValueAsString(#lead.phone)\n  + \', \"verification_status\": \' + #jsonMapper.writeValueAsString(#fields[\'verification_status\'])\n  + \', \"tcpa_optin\": \' + #jsonMapper.writeValueAsString(#fields[\'tcpaOptin\'] == \'true\' ? 1 : 0)\n  + \', \"carrier\": \' + #jsonMapper.writeValueAsString(#fields[\'carrier\'])\n  + \', \"carrierparent\": \' + #jsonMapper.writeValueAsString(#fields[\'parentCarrier\'])\n  + \', \"blacklisted\": \' + #jsonMapper.writeValueAsString(#fields[\'blacklisted\'])\n  + \', \"status\": \' + #jsonMapper.writeValueAsString(#fields[\'status\'])\n  + \', \"type\": \' + #jsonMapper.writeValueAsString(#fields[\'type\'])\n  + \', \"shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'shortURL\'])\n  + \', \"pl_shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'pl_shorturl\'])\n  + \', \"jluvr\": \' + #jsonMapper.writeValueAsString(#lead.jluvr)\n  + \', \"lead_id\" : \' + #jsonMapper.writeValueAsString(\"\" + #lead.id)\n+ \'}\'', NULL, NULL, NULL, '#response.contains(\'success\')', NULL, NULL, '{\"Authorization\":\"Basic bjhuOkV0cGNSTE1rR29CdA==\"}'),
(158, 'RCC SMS CCAI', 'Remarketing Control Center SMS Leads CCAI', 'https://control-center.nationsinfocorp.com/webhook/9f4cfddf-9a40-476c-93b1-3ea114b0a047', 20000, NULL, 'POST', 'JSON', NULL, NULL, NULL, '\'{\' \n  + \'\"emailaddress\": \' + #jsonMapper.writeValueAsString(#lead.email == null ? #fields[\'email\'] : #lead.email)\n  + \', \"brand_id\": 82\'\n  + \', \"firstname\": \' + #jsonMapper.writeValueAsString(#lead.firstName == null ? #fields[\'custfullname\'] : #lead.firstName)\n  + \', \"lastname\": \' + #jsonMapper.writeValueAsString(#lead.lastName)\n  + \', \"adid\": \' + #jsonMapper.writeValueAsString(#lead.idAd)\n  + \', \"pid\": \' + #jsonMapper.writeValueAsString(\"\" + #lead.idPartner)  \n  + \', \"leaddate\": \' + #jsonMapper.writeValueAsString(#lead.getFormattedLeadDate(\"yyyy-MM-dd hh:mm:ss a\"))\n  + \', \"customer_address_1\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_1\'])\n  + \', \"customer_address_2\": \' + #jsonMapper.writeValueAsString(#fields[\'customer_address_2\'])\n  + \', \"customer_city\": \' + #jsonMapper.writeValueAsString(#lead.city)\n  + \', \"customer_state\": \' + #jsonMapper.writeValueAsString(#lead.state)\n  + \', \"original_adid\": \' + #jsonMapper.writeValueAsString(#fields[\'original_adid\'])\n  + \', \"vertical_type\": \' + #jsonMapper.writeValueAsString(#lead.verticalType)\n  + \', \"zip\": \' + #jsonMapper.writeValueAsString(#lead.zip)\n  + \', \"customer_phone\": \' + #jsonMapper.writeValueAsString(#lead.phone)\n  + \', \"verification_status\": \' + #jsonMapper.writeValueAsString(#fields[\'verification_status\'])\n  + \', \"tcpa_optin\": \' + #jsonMapper.writeValueAsString(#fields[\'tcpaOptin\'] == \'true\' ? 1 : 0)\n  + \', \"carrier\": \' + #jsonMapper.writeValueAsString(#fields[\'carrier\'])\n  + \', \"carrierparent\": \' + #jsonMapper.writeValueAsString(#fields[\'parentCarrier\'])\n  + \', \"blacklisted\": \' + #jsonMapper.writeValueAsString(#fields[\'blacklisted\'])\n  + \', \"status\": \' + #jsonMapper.writeValueAsString(#fields[\'status\'])\n  + \', \"type\": \' + #jsonMapper.writeValueAsString(#fields[\'type\'])\n  + \', \"shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'shortURL\'])\n  + \', \"pl_shorturl\": \' + #jsonMapper.writeValueAsString(#fields[\'pl_shorturl\'])\n  + \', \"jluvr\": \' + #jsonMapper.writeValueAsString(#lead.jluvr)\n  + \', \"lead_id\" : \' + #jsonMapper.writeValueAsString(\"\" + #lead.id)\n+ \'}\'', NULL, NULL, NULL, '#response.contains(\'success\')', NULL, NULL, '{\"Authorization\":\"Basic bjhuOkV0cGNSTE1rR29CdA==\"}');
INSERT INTO `nic_common`.`lead_partner_campaigns`(`id_lead_partner_campaigns`, `ldm_version`, `id_lead_partner`, `name`, `description`, `status`, `accepting_lead_type`, `max_lead_retries`, `max_lead_age`, `lead_submit_delay`, `lead_submit_delay_type`, `priority`, `ratio`, `exclusivity`, `last_buyer`, `brand_id`, `ad_id`, `partner_id`, `super_partner_id`, `ad_location`, `lead_capture_location`, `capture_location`, `vertical_type`, `submitter`, `id_lead_submitter`, `daytime_restriction`, `tracking`, `url`, `api_variable`, `lead_limit`, `lead_limit_type`, `coverage_type`) 
VALUES 
(7654, 3, 5082, 'RCC SMS FHC', 'Remarketing Control Center SMS leads FHC', 'active', 'sms_fhc', 1, 127, 0, 'minutes', 2, NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RCC SMS FHC', 156, 'disabled', NULL, 'defined in submitter', 'defined in submitter', NULL, NULL, NULL),
(7655, 3, 5082, 'RCC SMS FRTO', 'Remarketing Control Center SMS leads FRTO', 'active', 'sms_frto', 1, 127, 0, 'minutes', 2, NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RCC SMS FRTO', 157, 'disabled', NULL, 'defined in submitter', 'defined in submitter', NULL, NULL, NULL),
(7656, 3, 5082, 'RCC SMS CCAI', 'Remarketing Control Center SMS leads CCAI', 'active', 'sms_ccai', 1, 127, 0, 'minutes', 2, NULL, b'0', b'0', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'RCC SMS CCAI', 158, 'disabled', NULL, 'defined in submitter', 'defined in submitter', NULL, NULL, NULL);