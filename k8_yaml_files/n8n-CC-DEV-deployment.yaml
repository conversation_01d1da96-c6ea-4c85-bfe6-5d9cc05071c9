apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n-cc-dev-deployment
  labels:
    app: n8n-cc-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n-cc-dev
  template:
    metadata:
      labels:
        app: n8n-cc-dev
    spec:
      containers:
        - name: n8n-cc-dev
          image: realtytraccontainerregistry.azurecr.io/n8n-control-center-dev:latest
          ports:
            - containerPort: 5678
          env:
            - name: DB_TYPE
              value: "postgresdb"
            - name: DB_POSTGRESDB_DATABASE
              value: "n8n" 
            - name: DB_POSTGRESDB_HOST
              value: "*************"
            - name: DB_POSTGRESDB_PORT
              value: "5432"
            - name: DB_POSTGRESDB_USER
              value: ""
            - name: DB_POSTGRESDB_SCHEMA
              value: "n8n"
            - name: DB_POSTGRESDB_PASSWORD
              value: ""
            - name: SSL_CERT_DIR
              value: /opt/custom-certificates
            - name: WEBHOOK_URL
              value: https://control-center-dev.nationsinfocorp.com/              
          resources:
      imagePullSecrets:
        - name: azure-dev