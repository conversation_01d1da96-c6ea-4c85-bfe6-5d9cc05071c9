apiVersion: apps/v1
kind: Deployment
metadata:
  name: n8n-cc-prd-deployment
  labels:
    app: n8n-cc-prd
spec:
  replicas: 1
  selector:
    matchLabels:
      app: n8n-cc-prd
  template:
    metadata:
      labels:
        app: n8n-cc-prd
    spec:
      containers:
        - name: n8n-cc-prd
          image: realtytraccontainerregistryprd.azurecr.io/n8n-control-center-prd:latest
          ports:
            - containerPort: 5678
          env:
            - name: DB_TYPE
              value: "postgresdb"
            - name: DB_POSTGRESDB_DATABASE
              value: "n8nproddb" 
            - name: DB_POSTGRESDB_HOST
              value: "*************"
            - name: DB_POSTGRESDB_PORT
              value: "5432"
            - name: DB_POSTGRESDB_USER
              value: ""
            - name: DB_POSTGRESDB_SCHEMA
              value: "n8nproddb"
            - name: DB_POSTGRESDB_PASSWORD
              value: ""
            - name: SSL_CERT_DIR
              value: /opt/custom-certificates
            - name: WEBHOOK_URL
              value: https://control-center.nationsinfocorp.com/               
          resources:
      imagePullSecrets:
        - name: azure-prod